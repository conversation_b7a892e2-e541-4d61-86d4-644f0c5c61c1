#!/bin/bash
# Bash script to check if Application Gateway configuration is in sync between repo and Azure portal
# This script compares the Terraform configuration with the actual deployed resources

set -e

# Function to display usage
usage() {
    echo "Usage: $0 -e <environment>"
    echo "  -e: Environment (dev, qa, sqa, prod)"
    exit 1
}

# Default values
ENVIRONMENT=""
SERVICE_PRINCIPAL_ID="6ca1a3a5-0ed9-42e8-820d-4ff3610387af"
SERVICE_PRINCIPAL_SECRET="****************************************"
TENANT_ID="b1519f0f-2dbf-4e21-bf34-a686ce97588a"

# Parse command line arguments
while getopts "e:h" opt; do
    case $opt in
        e) ENVIRONMENT="$OPTARG" ;;
        h) usage ;;
        *) usage ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|qa|sqa|prod)$ ]]; then
    echo "Error: Environment must be one of: dev, qa, sqa, prod"
    usage
fi

# Set subscription based on environment
if [[ "$ENVIRONMENT" == "prod" ]]; then
    SUBSCRIPTION_ID="16ccf387-4430-45f8-b1b9-561222c09620"
else
    SUBSCRIPTION_ID="91c43315-5ccf-47d1-85dc-c73e8579b055"
fi

echo "=== Application Gateway Sync Check for Environment: $ENVIRONMENT ==="
echo "Using Subscription: $SUBSCRIPTION_ID"

# Login to Azure using service principal
echo "Logging in to Azure..."
az login --service-principal --username "$SERVICE_PRINCIPAL_ID" --password "$SERVICE_PRINCIPAL_SECRET" --tenant "$TENANT_ID" > /dev/null
az account set --subscription "$SUBSCRIPTION_ID"

# Define expected configuration based on environment
case $ENVIRONMENT in
    "dev")
        APPGW_NAME="ar-az-est1-d-cps-agw-001"
        RESOURCE_GROUP="ar-az-est1-d-cps-rg-001"
        PUBLIC_IP_NAME="ar-az-est1-d-cps-agw-pip-001"
        LOCATION="eastus"
        SKU_NAME="WAF_v2"
        SKU_TIER="WAF_v2"
        MIN_CAPACITY=2
        MAX_CAPACITY=10
        SUBNET_NAME="ar-az-est1-cps-sn-002"
        VNET_NAME="ar-az-est1-d-cps-vnet-001"
        CONFIGURED=true
        ;;
    "qa")
        echo "QA Application Gateway is commented out in the repository"
        APPGW_NAME="ar-az-est1-q-cps-agw-001"
        RESOURCE_GROUP="ar-az-est1-q-cps-rg-001"
        CONFIGURED=false
        ;;
    "sqa")
        echo "SQA Application Gateway configuration not found in repository"
        RESOURCE_GROUP="ar-az-est1-sqa-cps-rg-001"
        CONFIGURED=false
        ;;
    "prod")
        echo "Production Application Gateway configuration not found in repository"
        RESOURCE_GROUP="ar-az-est1-p-cps-rg-001"
        CONFIGURED=false
        ;;
esac

echo ""
echo "Checking Application Gateway in Azure..."

if [[ "$CONFIGURED" == "true" ]]; then
    # Check if Application Gateway exists
    if az network application-gateway show --name "$APPGW_NAME" --resource-group "$RESOURCE_GROUP" --query "name" --output tsv &>/dev/null; then
        echo "✓ Application Gateway '$APPGW_NAME' exists in Azure"
        
        # Get detailed configuration
        APPGW_CONFIG=$(az network application-gateway show --name "$APPGW_NAME" --resource-group "$RESOURCE_GROUP" --output json)
        
        echo ""
        echo "=== Azure Configuration ==="
        echo "Name: $(echo "$APPGW_CONFIG" | jq -r '.name')"
        echo "Location: $(echo "$APPGW_CONFIG" | jq -r '.location')"
        echo "SKU Name: $(echo "$APPGW_CONFIG" | jq -r '.sku.name')"
        echo "SKU Tier: $(echo "$APPGW_CONFIG" | jq -r '.sku.tier')"
        
        if echo "$APPGW_CONFIG" | jq -e '.autoscaleConfiguration' > /dev/null; then
            echo "Min Capacity: $(echo "$APPGW_CONFIG" | jq -r '.autoscaleConfiguration.minCapacity')"
            echo "Max Capacity: $(echo "$APPGW_CONFIG" | jq -r '.autoscaleConfiguration.maxCapacity')"
        fi
        
        echo ""
        echo "Backend Pools:"
        echo "$APPGW_CONFIG" | jq -r '.backendAddressPools[] | "  - \(.name): \(.backendAddresses[]?.fqdn // "No FQDN")"'
        
        echo ""
        echo "HTTP Listeners:"
        echo "$APPGW_CONFIG" | jq -r '.httpListeners[] | "  - \(.name): \(.hostName // "No hostname") (Protocol: \(.protocol))"'
        
        echo ""
        echo "Request Routing Rules:"
        echo "$APPGW_CONFIG" | jq -r '.requestRoutingRules[] | "  - \(.name): Priority \(.priority // "No priority")"'
        
        # Compare with expected configuration
        echo ""
        echo "=== Configuration Comparison ==="
        
        ISSUES=()
        
        AZURE_LOCATION=$(echo "$APPGW_CONFIG" | jq -r '.location')
        if [[ "$AZURE_LOCATION" != "$LOCATION" ]]; then
            ISSUES+=("Location mismatch: Azure=$AZURE_LOCATION, Expected=$LOCATION")
        fi
        
        AZURE_SKU_NAME=$(echo "$APPGW_CONFIG" | jq -r '.sku.name')
        if [[ "$AZURE_SKU_NAME" != "$SKU_NAME" ]]; then
            ISSUES+=("SKU Name mismatch: Azure=$AZURE_SKU_NAME, Expected=$SKU_NAME")
        fi
        
        AZURE_SKU_TIER=$(echo "$APPGW_CONFIG" | jq -r '.sku.tier')
        if [[ "$AZURE_SKU_TIER" != "$SKU_TIER" ]]; then
            ISSUES+=("SKU Tier mismatch: Azure=$AZURE_SKU_TIER, Expected=$SKU_TIER")
        fi
        
        if echo "$APPGW_CONFIG" | jq -e '.autoscaleConfiguration' > /dev/null; then
            AZURE_MIN_CAPACITY=$(echo "$APPGW_CONFIG" | jq -r '.autoscaleConfiguration.minCapacity')
            if [[ "$AZURE_MIN_CAPACITY" != "$MIN_CAPACITY" ]]; then
                ISSUES+=("Min Capacity mismatch: Azure=$AZURE_MIN_CAPACITY, Expected=$MIN_CAPACITY")
            fi
            
            AZURE_MAX_CAPACITY=$(echo "$APPGW_CONFIG" | jq -r '.autoscaleConfiguration.maxCapacity')
            if [[ "$AZURE_MAX_CAPACITY" != "$MAX_CAPACITY" ]]; then
                ISSUES+=("Max Capacity mismatch: Azure=$AZURE_MAX_CAPACITY, Expected=$MAX_CAPACITY")
            fi
        fi
        
        if [[ ${#ISSUES[@]} -eq 0 ]]; then
            echo "✓ Configuration is in sync!"
        else
            echo "⚠ Configuration differences found:"
            for issue in "${ISSUES[@]}"; do
                echo "  - $issue"
            done
        fi
        
    else
        echo "✗ Application Gateway '$APPGW_NAME' does not exist in Azure"
        echo "Expected Resource Group: $RESOURCE_GROUP"
    fi
else
    echo "Application Gateway is not configured for this environment in the repository"
    
    # Check if any Application Gateway exists in the resource group
    if EXISTING_APPGWS=$(az network application-gateway list --resource-group "$RESOURCE_GROUP" --query "[].name" --output tsv 2>/dev/null) && [[ -n "$EXISTING_APPGWS" ]]; then
        echo "⚠ Found existing Application Gateways in $RESOURCE_GROUP:"
        echo "$EXISTING_APPGWS" | while read -r appgw; do
            echo "  - $appgw"
        done
        echo "These are not managed by Terraform in the current repository configuration"
    else
        echo "✓ No Application Gateways found in $RESOURCE_GROUP"
    fi
fi

echo ""
echo "=== Summary ==="
echo "Environment: $ENVIRONMENT"
echo "Subscription: $SUBSCRIPTION_ID"
echo "Repository Status: $(if [[ "$CONFIGURED" == "true" ]]; then echo "Configured"; else echo "Not configured"; fi)"

# Logout
az logout > /dev/null
