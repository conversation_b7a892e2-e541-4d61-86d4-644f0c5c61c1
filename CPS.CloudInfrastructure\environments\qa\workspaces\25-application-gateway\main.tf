# data "azurerm_subnet" "appgw_subnet" {
#   name                 = var.appgw_subnet_name
#   virtual_network_name = "ar-az-est1-q-cps-vnet-001"
#   resource_group_name  = "ar-az-est1-q-cps-rg-001"
# }

# module "application_gateway" {
#   source = "../../../../modules/application-gateway"

#   appgw_name          = var.application_gateway_name
#   location            = var.location
#   resource_group_name = var.resource_group_name
#   public_ip_name      = var.public_ip_name
#   sku_name            = var.sku_name
#   sku_tier            = var.sku_tier
#   # Using autoscaling instead of fixed capacity
#   min_capacity = var.min_capacity
#   max_capacity = var.max_capacity
#   subnet_id    = data.azurerm_subnet.appgw_subnet.id
#   tags         = var.tags
#   domains      = var.domains
#   # Empty webapp_services for new domain-based configuration
#   webapp_services = {}
# }
