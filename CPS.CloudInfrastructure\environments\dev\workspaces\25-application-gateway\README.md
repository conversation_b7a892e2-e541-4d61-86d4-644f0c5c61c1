# Centralized Application Gateway Configuration

## 🚀 Overview
This workspace manages the **ONLY Application Gateway** for all non-production environments (dev, qa, sqa).

**⚠️ Important**:
- **QA and SQA environments do NOT have their own Application Gateway workspaces**
- **ALL Application Gateway changes for dev/qa/sqa are made HERE**
- This provides cost efficiency and simplified management while maintaining environment isolation through different domains

## Architecture
```
Single Application Gateway: ar-az-est1-d-cps-agw-001
├── dev-app.ecps.ca → Dev Admin Storage + API
├── dev-supplier.ecps.ca → Dev Supplier Storage + API
├── qa-app.ecps.ca → QA Admin Storage + API
├── qa-supplier.ecps.ca → QA Supplier Storage + API
├── sqa-app.ecps.ca → SQA Admin Storage + API
└── sqa-supplier.ecps.ca → SQA Supplier Storage + API
```

## Configuration Details

### Current Domains
| Domain | Environment | App Backend | API Backend |
|--------|-------------|-------------|-------------|
| `dev-app.ecps.ca` | Development | `arazest1dadminst001.z13.web.core.windows.net` | `ar-az-est1-d-cpsadmin-web-001.azurewebsites.net` |
| `dev-supplier.ecps.ca` | Development | `arazest1dsupplierst001.z13.web.core.windows.net` | `ar-az-est1-d-cpssupplier-web-001.azurewebsites.net` |
| `qa-app.ecps.ca` | QA | `arazest1qadminst001.z13.web.core.windows.net` | `ar-az-est1-q-cpsadmin-web-001.azurewebsites.net` |
| `qa-supplier.ecps.ca` | QA | `arazest1qsupplierst001.z13.web.core.windows.net` | `ar-az-est1-q-cpssupplier-web-001.azurewebsites.net` |
| `sqa-app.ecps.ca` | SQA | `arazest1sqaadminst001.z13.web.core.windows.net` | `ar-az-est1-sqa-cpsadmin-web-001.azurewebsites.net` |
| `sqa-supplier.ecps.ca` | SQA | `arazest1sqasupplierst001.z13.web.core.windows.net` | `ar-az-est1-sqa-cpssupplier-web-001.azurewebsites.net` |

### Routing Rules
- **Root path (`/`)**: Routes to the app backend (Storage Account for SPA)
- **API path (`/api/*`)**: Routes to the api backend (Web App)

## Adding New Environments

To add a new environment (e.g., `staging`), simply add the domains to the `domains` map in `terraform.tfvars`:

```hcl
domains = {
  # ... existing domains ...
  
  # New Staging Environment
  "staging-app.ecps.ca" = {
    app_backend = {
      host_name   = "arazest1stagingadminst001.z13.web.core.windows.net"
      type        = "storage"
      health_path = "/"
    }
    api_backend = {
      host_name   = "ar-az-est1-staging-cpsadmin-web-001.azurewebsites.net"
      type        = "webapp"
      health_path = "/"
    }
  }
  "staging-supplier.ecps.ca" = {
    app_backend = {
      host_name   = "arazest1stagingsupplierst001.z13.web.core.windows.net"
      type        = "storage"
      health_path = "/"
    }
    api_backend = {
      host_name   = "ar-az-est1-staging-cpssupplier-web-001.azurewebsites.net"
      type        = "webapp"
      health_path = "/"
    }
  }
}
```

## Deployment

### Prerequisites
1. Ensure all backend services (Storage Accounts and Web Apps) exist for the environment
2. DNS records should point to the Application Gateway's public IP

### Deploy Changes
```bash
cd environments/dev/workspaces/25-application-gateway
terraform plan
terraform apply
```

## Benefits of This Approach

1. **Cost Efficiency**: Single Application Gateway instead of multiple
2. **Simplified Management**: One configuration to manage all non-prod environments
3. **Easy Scaling**: Add new environments by just updating the configuration
4. **Consistent Routing**: Same routing logic across all environments
5. **Centralized SSL/TLS**: Manage certificates in one place

## Important Notes

- **Production Isolation**: Production environment should have its own separate Application Gateway
- **DNS Configuration**: Each domain needs proper DNS records pointing to the Application Gateway's public IP
- **SSL Certificates**: Configure SSL certificates for HTTPS domains
- **Health Checks**: Each backend has its own health check configuration

## Related Configurations

- **QA Environment**: ❌ **NO separate workspace** - handled here
- **SQA Environment**: ❌ **NO separate workspace** - handled here
- **Production**: ✅ Should have its own separate Application Gateway workspace

### Workspace Structure
```
environments/
├── dev/workspaces/25-application-gateway/     ← 🎯 CENTRALIZED (handles dev+qa+sqa)
├── qa/workspaces/                             ← ❌ NO 25-application-gateway folder
├── sqa/workspaces/                            ← ❌ NO 25-application-gateway folder
└── prod/workspaces/25-application-gateway/    ← 🎯 SEPARATE (production only)
```

## Troubleshooting

1. **Backend Health Issues**: Check if the backend services are running and accessible
2. **DNS Resolution**: Verify DNS records point to the correct Application Gateway IP
3. **SSL Certificate Issues**: Ensure certificates are properly configured for HTTPS domains
4. **Routing Problems**: Check the path-based routing rules in the Application Gateway configuration
