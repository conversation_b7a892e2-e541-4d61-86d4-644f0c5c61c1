# PowerShell script to check if Application Gateway configuration is in sync between repo and Azure portal
# This script compares the Terraform configuration with the actual deployed resources

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "qa", "sqa", "prod")]
    [string]$Environment,

    [Parameter(Mandatory=$false)]
    [string]$ServicePrincipalId = "6ca1a3a5-0ed9-42e8-820d-4ff3610387af",

    [Parameter(Mandatory=$false)]
    [string]$ServicePrincipalSecret = "****************************************",

    [Parameter(Mandatory=$false)]
    [string]$TenantId = "b1519f0f-2dbf-4e21-bf34-a686ce97588a"
)

# Set subscription based on environment
$subscriptionId = if ($Environment -eq "prod") { "16ccf387-4430-45f8-b1b9-561222c09620" } else { "91c43315-5ccf-47d1-85dc-c73e8579b055" }

Write-Host "=== Application Gateway Sync Check for Environment: $Environment ===" -ForegroundColor Green
Write-Host "Using Subscription: $subscriptionId" -ForegroundColor Yellow

# Login to Azure using service principal
Write-Host "Logging in to Azure..." -ForegroundColor Yellow
az login --service-principal --username $ServicePrincipalId --password $ServicePrincipalSecret --tenant $TenantId
az account set --subscription $subscriptionId

# Define expected configuration based on environment
$expectedConfig = @{}

switch ($Environment) {
    "dev" {
        $expectedConfig = @{
            AppGwName = "ar-az-est1-d-cps-agw-001"
            ResourceGroup = "ar-az-est1-d-cps-rg-001"
            PublicIpName = "ar-az-est1-d-cps-agw-pip-001"
            Location = "eastus"
            SkuName = "WAF_v2"
            SkuTier = "WAF_v2"
            MinCapacity = 2
            MaxCapacity = 10
            SubnetName = "ar-az-est1-cps-sn-002"
            VNetName = "ar-az-est1-d-cps-vnet-001"
            Domains = @{
                "dev-app.ecps.ca" = @{
                    AppBackend = "arazest1dadminst001.z13.web.core.windows.net"
                    ApiBackend = "ar-az-est1-d-cpsadmin-web-001.azurewebsites.net"
                }
                "dev-supplier.ecps.ca" = @{
                    AppBackend = "arazest1dsupplierst001.z13.web.core.windows.net"
                    ApiBackend = "ar-az-est1-d-cpssupplier-web-001.azurewebsites.net"
                }
            }
        }
    }
    "qa" {
        Write-Host "QA Application Gateway is commented out in the repository" -ForegroundColor Yellow
        $expectedConfig = @{
            AppGwName = "ar-az-est1-q-cps-agw-001"
            ResourceGroup = "ar-az-est1-q-cps-rg-001"
            Status = "Not configured in repository"
        }
    }
    "sqa" {
        Write-Host "SQA Application Gateway configuration not found in repository" -ForegroundColor Yellow
        $expectedConfig = @{
            Status = "Not configured in repository"
        }
    }
    "prod" {
        Write-Host "Production Application Gateway configuration not found in repository" -ForegroundColor Yellow
        $expectedConfig = @{
            Status = "Not configured in repository"
        }
    }
}

# Check if Application Gateway exists in Azure
Write-Host "`nChecking Application Gateway in Azure..." -ForegroundColor Yellow

if ($expectedConfig.ContainsKey("AppGwName")) {
    $appGwExists = az network application-gateway show --name $expectedConfig.AppGwName --resource-group $expectedConfig.ResourceGroup --query "name" --output tsv 2>$null
    
    if ($appGwExists) {
        Write-Host "✓ Application Gateway '$($expectedConfig.AppGwName)' exists in Azure" -ForegroundColor Green
        
        # Get detailed configuration
        $appGwConfig = az network application-gateway show --name $expectedConfig.AppGwName --resource-group $expectedConfig.ResourceGroup --output json | ConvertFrom-Json
        
        Write-Host "`n=== Azure Configuration ===" -ForegroundColor Cyan
        Write-Host "Name: $($appGwConfig.name)"
        Write-Host "Location: $($appGwConfig.location)"
        Write-Host "SKU Name: $($appGwConfig.sku.name)"
        Write-Host "SKU Tier: $($appGwConfig.sku.tier)"
        
        if ($appGwConfig.autoscaleConfiguration) {
            Write-Host "Min Capacity: $($appGwConfig.autoscaleConfiguration.minCapacity)"
            Write-Host "Max Capacity: $($appGwConfig.autoscaleConfiguration.maxCapacity)"
        }
        
        Write-Host "`nBackend Pools:"
        foreach ($pool in $appGwConfig.backendAddressPools) {
            Write-Host "  - $($pool.name): $($pool.backendAddresses.fqdn -join ', ')"
        }
        
        Write-Host "`nHTTP Listeners:"
        foreach ($listener in $appGwConfig.httpListeners) {
            Write-Host "  - $($listener.name): $($listener.hostName) (Protocol: $($listener.protocol))"
        }
        
        Write-Host "`nRequest Routing Rules:"
        foreach ($rule in $appGwConfig.requestRoutingRules) {
            Write-Host "  - $($rule.name): Priority $($rule.priority)"
        }
        
        # Compare with expected configuration
        Write-Host "`n=== Configuration Comparison ===" -ForegroundColor Cyan
        
        $issues = @()
        
        if ($appGwConfig.location -ne $expectedConfig.Location) {
            $issues += "Location mismatch: Azure=$($appGwConfig.location), Expected=$($expectedConfig.Location)"
        }
        
        if ($appGwConfig.sku.name -ne $expectedConfig.SkuName) {
            $issues += "SKU Name mismatch: Azure=$($appGwConfig.sku.name), Expected=$($expectedConfig.SkuName)"
        }
        
        if ($appGwConfig.sku.tier -ne $expectedConfig.SkuTier) {
            $issues += "SKU Tier mismatch: Azure=$($appGwConfig.sku.tier), Expected=$($expectedConfig.SkuTier)"
        }
        
        if ($appGwConfig.autoscaleConfiguration) {
            if ($appGwConfig.autoscaleConfiguration.minCapacity -ne $expectedConfig.MinCapacity) {
                $issues += "Min Capacity mismatch: Azure=$($appGwConfig.autoscaleConfiguration.minCapacity), Expected=$($expectedConfig.MinCapacity)"
            }
            if ($appGwConfig.autoscaleConfiguration.maxCapacity -ne $expectedConfig.MaxCapacity) {
                $issues += "Max Capacity mismatch: Azure=$($appGwConfig.autoscaleConfiguration.maxCapacity), Expected=$($expectedConfig.MaxCapacity)"
            }
        }
        
        if ($issues.Count -eq 0) {
            Write-Host "✓ Configuration is in sync!" -ForegroundColor Green
        } else {
            Write-Host "⚠ Configuration differences found:" -ForegroundColor Yellow
            foreach ($issue in $issues) {
                Write-Host "  - $issue" -ForegroundColor Red
            }
        }
        
    } else {
        Write-Host "✗ Application Gateway '$($expectedConfig.AppGwName)' does not exist in Azure" -ForegroundColor Red
        Write-Host "Expected Resource Group: $($expectedConfig.ResourceGroup)" -ForegroundColor Yellow
    }
} else {
    Write-Host "Application Gateway is not configured for this environment in the repository" -ForegroundColor Yellow
    
    # Check if any Application Gateway exists in the resource group
    $resourceGroup = switch ($Environment) {
        "qa" { "ar-az-est1-q-cps-rg-001" }
        "sqa" { "ar-az-est1-sqa-cps-rg-001" }
        "prod" { "ar-az-est1-p-cps-rg-001" }
    }
    
    if ($resourceGroup) {
        $existingAppGws = az network application-gateway list --resource-group $resourceGroup --query "[].name" --output tsv 2>$null
        if ($existingAppGws) {
            Write-Host "⚠ Found existing Application Gateways in $resourceGroup :" -ForegroundColor Yellow
            foreach ($appgw in $existingAppGws) {
                Write-Host "  - $appgw" -ForegroundColor Cyan
            }
            Write-Host "These are not managed by Terraform in the current repository configuration" -ForegroundColor Red
        } else {
            Write-Host "✓ No Application Gateways found in $resourceGroup" -ForegroundColor Green
        }
    }
}

Write-Host "`n=== Summary ===" -ForegroundColor Green
Write-Host "Environment: $Environment"
Write-Host "Subscription: $subscriptionId"
$repoStatus = if ($expectedConfig.ContainsKey("Status")) { $expectedConfig.Status } else { "Configured" }
Write-Host "Repository Status: $repoStatus"

# Logout
az logout
