# Script to validate the centralized Application Gateway configuration
# This script checks if all required backend services exist for the configured domains

param(
    [Parameter(Mandatory=$false)]
    [string]$ServicePrincipalId = "6ca1a3a5-0ed9-42e8-820d-4ff3610387af",
    
    [Parameter(Mandatory=$false)]
    [string]$ServicePrincipalSecret = "****************************************",
    
    [Parameter(Mandatory=$false)]
    [string]$TenantId = "b1519f0f-2dbf-4e21-bf34-a686ce97588a"
)

Write-Host "=== Centralized Application Gateway Configuration Validation ===" -ForegroundColor Green

# Login to Azure
Write-Host "Logging in to Azure..." -ForegroundColor Yellow
az login --service-principal --username $ServicePrincipalId --password $ServicePrincipalSecret --tenant $TenantId > $null
az account set --subscription "91c43315-5ccf-47d1-85dc-c73e8579b055"

# Define the expected backend services based on terraform.tfvars
$expectedBackends = @{
    "dev" = @{
        "admin_storage" = "arazest1dadminst001"
        "admin_webapp" = "ar-az-est1-d-cpsadmin-web-001"
        "supplier_storage" = "arazest1dsupplierst001"
        "supplier_webapp" = "ar-az-est1-d-cpssupplier-web-001"
    }
    "qa" = @{
        "admin_storage" = "arazest1qadminst001"
        "admin_webapp" = "ar-az-est1-q-cpsadmin-web-001"
        "supplier_storage" = "arazest1qsupplierst001"
        "supplier_webapp" = "ar-az-est1-q-cpssupplier-web-001"
    }
    "sqa" = @{
        "admin_storage" = "arazest1sqaadminst001"
        "admin_webapp" = "ar-az-est1-sqa-cpsadmin-web-001"
        "supplier_storage" = "arazest1sqasupplierst001"
        "supplier_webapp" = "ar-az-est1-sqa-cpssupplier-web-001"
    }
}

$resourceGroups = @{
    "dev" = "ar-az-est1-d-cps-rg-001"
    "qa" = "ar-az-est1-q-cps-rg-001"
    "sqa" = "ar-az-est1-sqa-cps-rg-001"
}

Write-Host "`n=== Validating Backend Services ===" -ForegroundColor Cyan

$allValid = $true

foreach ($env in $expectedBackends.Keys) {
    Write-Host "`nChecking $env environment..." -ForegroundColor Yellow
    $rg = $resourceGroups[$env]
    
    # Check if resource group exists
    $rgExists = az group exists --name $rg --output tsv
    if ($rgExists -eq "false") {
        Write-Host "  ✗ Resource Group '$rg' does not exist" -ForegroundColor Red
        $allValid = $false
        continue
    } else {
        Write-Host "  ✓ Resource Group '$rg' exists" -ForegroundColor Green
    }
    
    $backends = $expectedBackends[$env]
    
    # Check Storage Accounts
    foreach ($storageKey in @("admin_storage", "supplier_storage")) {
        $storageName = $backends[$storageKey]
        $storageExists = az storage account show --name $storageName --resource-group $rg --query "name" --output tsv 2>$null
        
        if ($storageExists) {
            Write-Host "  ✓ Storage Account '$storageName' exists" -ForegroundColor Green
            
            # Check if static website is enabled
            $staticWebsite = az storage blob service-properties show --account-name $storageName --query "staticWebsite.enabled" --output tsv 2>$null
            if ($staticWebsite -eq "true") {
                Write-Host "    ✓ Static website enabled" -ForegroundColor Green
            } else {
                Write-Host "    ⚠ Static website not enabled" -ForegroundColor Yellow
            }
        } else {
            Write-Host "  ✗ Storage Account '$storageName' does not exist" -ForegroundColor Red
            $allValid = $false
        }
    }
    
    # Check Web Apps
    foreach ($webappKey in @("admin_webapp", "supplier_webapp")) {
        $webappName = $backends[$webappKey]
        $webappExists = az webapp show --name $webappName --resource-group $rg --query "name" --output tsv 2>$null
        
        if ($webappExists) {
            Write-Host "  ✓ Web App '$webappName' exists" -ForegroundColor Green
            
            # Check if web app is running
            $webappState = az webapp show --name $webappName --resource-group $rg --query "state" --output tsv 2>$null
            if ($webappState -eq "Running") {
                Write-Host "    ✓ Web App is running" -ForegroundColor Green
            } else {
                Write-Host "    ⚠ Web App state: $webappState" -ForegroundColor Yellow
            }
        } else {
            Write-Host "  ✗ Web App '$webappName' does not exist" -ForegroundColor Red
            $allValid = $false
        }
    }
}

# Check Application Gateway
Write-Host "`n=== Validating Application Gateway ===" -ForegroundColor Cyan
$appgwName = "ar-az-est1-d-cps-agw-001"
$appgwRg = "ar-az-est1-d-cps-rg-001"

$appgwExists = az network application-gateway show --name $appgwName --resource-group $appgwRg --query "name" --output tsv 2>$null

if ($appgwExists) {
    Write-Host "✓ Application Gateway '$appgwName' exists" -ForegroundColor Green
    
    # Get backend pools
    $backendPools = az network application-gateway address-pool list --gateway-name $appgwName --resource-group $appgwRg --query "[].name" --output tsv
    Write-Host "Backend Pools: $($backendPools -join ', ')" -ForegroundColor Cyan
    
    # Get listeners
    $listeners = az network application-gateway http-listener list --gateway-name $appgwName --resource-group $appgwRg --query "[].name" --output tsv
    Write-Host "HTTP Listeners: $($listeners -join ', ')" -ForegroundColor Cyan
    
} else {
    Write-Host "✗ Application Gateway '$appgwName' does not exist" -ForegroundColor Red
    $allValid = $false
}

# Summary
Write-Host "`n=== Validation Summary ===" -ForegroundColor Green
if ($allValid) {
    Write-Host "✓ All backend services are ready for the centralized Application Gateway" -ForegroundColor Green
    Write-Host "You can proceed with terraform apply" -ForegroundColor Green
} else {
    Write-Host "⚠ Some backend services are missing or not configured properly" -ForegroundColor Yellow
    Write-Host "Please ensure all required resources exist before applying the Application Gateway configuration" -ForegroundColor Yellow
}

# Logout
az logout > $null
