data "azurerm_subnet" "appgw_subnet" {
  name                 = var.appgw_subnet_name
  virtual_network_name = "ar-az-est1-d-cps-vnet-001"
  resource_group_name  = "ar-az-est1-d-cps-rg-001"
}

# Reference to Key Vaults for SSL certificates
data "azurerm_key_vault" "dev" {
  name                = "ar-az-est1-d-cps-kv-001"
  resource_group_name = "ar-az-est1-d-cps-rg-001"
}

data "azurerm_key_vault" "qa" {
  name                = "ar-az-est1-q-cps-kv-001"
  resource_group_name = "ar-az-est1-q-cps-rg-001"
}

data "azurerm_key_vault" "sqa" {
  name                = "ar-az-est1-sqa-kv-001"
  resource_group_name = "ar-az-est1-sqa-cps-rg-001"
}

module "application_gateway" {
  source = "../../../../modules/application-gateway"

  appgw_name          = var.application_gateway_name
  location            = var.location
  resource_group_name = var.resource_group_name
  public_ip_name      = var.public_ip_name
  sku_name            = var.sku_name
  sku_tier            = var.sku_tier
  # Using autoscaling instead of fixed capacity
  min_capacity = var.min_capacity
  max_capacity = var.max_capacity
  subnet_id    = data.azurerm_subnet.appgw_subnet.id
  tags         = var.tags
  domains      = var.domains
  # Empty webapp_services for new domain-based configuration
  webapp_services = {}

  # Enable HTTPS with SSL certificates from Key Vault
  enable_https = true
  key_vault_id = data.azurerm_key_vault.dev.id
  ssl_certificates = {
    # Dev environment certificates (from dev Key Vault)
    "dev-app-ecps-ca" = {
      name                = "dev-app-ecps-ca"
      key_vault_secret_id = "${data.azurerm_key_vault.dev.vault_uri}secrets/dev-app-ecps-ca"
    }
    "dev-supplier-ecps-ca" = {
      name                = "dev-supplier-ecps-ca"
      key_vault_secret_id = "${data.azurerm_key_vault.dev.vault_uri}secrets/dev-supplier-ecps-ca"
    }
    # QA environment certificates (from qa Key Vault)
    "qa-app-ecps-ca" = {
      name                = "qa-app-ecps-ca"
      key_vault_secret_id = "${data.azurerm_key_vault.qa.vault_uri}secrets/qa-app-ecps-ca"
    }
    "qa-supplier-ecps-ca" = {
      name                = "qa-supplier-ecps-ca"
      key_vault_secret_id = "${data.azurerm_key_vault.qa.vault_uri}secrets/qa-supplier-ecps-ca"
    }
    # SQA environment certificates (from sqa Key Vault)
    "sqa-app-ecps-ca" = {
      name                = "sqa-app-ecps-ca"
      key_vault_secret_id = "${data.azurerm_key_vault.sqa.vault_uri}secrets/sqa-app-ecps-ca"
    }
    "sqa-supplier-ecps-ca" = {
      name                = "sqa-supplier-ecps-ca"
      key_vault_secret_id = "${data.azurerm_key_vault.sqa.vault_uri}secrets/sqa-supplier-ecps-ca"
    }
  }
}
