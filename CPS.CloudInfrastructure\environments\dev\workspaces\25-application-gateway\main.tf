data "azurerm_subnet" "appgw_subnet" {
  name                 = var.appgw_subnet_name
  virtual_network_name = "ar-az-est1-d-cps-vnet-001"
  resource_group_name  = "ar-az-est1-d-cps-rg-001"
}

# Reference to the Key Vault for SSL certificates
data "azurerm_key_vault" "main" {
  name                = "ar-az-est1-d-cps-kv-001"
  resource_group_name = "ar-az-est1-d-cps-rg-001"
}

module "application_gateway" {
  source = "../../../../modules/application-gateway"

  appgw_name          = var.application_gateway_name
  location            = var.location
  resource_group_name = var.resource_group_name
  public_ip_name      = var.public_ip_name
  sku_name            = var.sku_name
  sku_tier            = var.sku_tier
  # Using autoscaling instead of fixed capacity
  min_capacity = var.min_capacity
  max_capacity = var.max_capacity
  subnet_id    = data.azurerm_subnet.appgw_subnet.id
  tags         = var.tags
  domains      = var.domains
  # Empty webapp_services for new domain-based configuration
  webapp_services = {}

  # Enable HTTPS with SSL certificates from Key Vault
  enable_https = true
  key_vault_id = data.azurerm_key_vault.main.id
  ssl_certificates = {
    "cert-dev-app-ecps-ca" = {
      name                = "cert-dev-app-ecps-ca"
      key_vault_secret_id = "${data.azurerm_key_vault.main.vault_uri}secrets/dev-app-ecps-ca"
    }
    "cert-dev-supplier-ecps-ca" = {
      name                = "cert-dev-supplier-ecps-ca"
      key_vault_secret_id = "${data.azurerm_key_vault.main.vault_uri}secrets/dev-supplier-ecps-ca"
    }
    "cert-qa-app-ecps-ca" = {
      name                = "cert-qa-app-ecps-ca"
      key_vault_secret_id = "${data.azurerm_key_vault.main.vault_uri}secrets/qa-app-ecps-ca"
    }
    "cert-qa-supplier-ecps-ca" = {
      name                = "cert-qa-supplier-ecps-ca"
      key_vault_secret_id = "${data.azurerm_key_vault.main.vault_uri}secrets/qa-supplier-ecps-ca"
    }
    "cert-sqa-app-ecps-ca" = {
      name                = "cert-sqa-app-ecps-ca"
      key_vault_secret_id = "${data.azurerm_key_vault.main.vault_uri}secrets/sqa-app-ecps-ca"
    }
    "cert-sqa-supplier-ecps-ca" = {
      name                = "cert-sqa-supplier-ecps-ca"
      key_vault_secret_id = "${data.azurerm_key_vault.main.vault_uri}secrets/sqa-supplier-ecps-ca"
    }
  }
}
