# Simple PowerShell script to check Application Gateway sync
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "qa", "sqa", "prod")]
    [string]$Environment
)

# Set subscription based on environment
if ($Environment -eq "prod") {
    $subscriptionId = "16ccf387-4430-45f8-b1b9-561222c09620"
} else {
    $subscriptionId = "91c43315-5ccf-47d1-85dc-c73e8579b055"
}

Write-Host "=== Application Gateway Sync Check for Environment: $Environment ===" -ForegroundColor Green
Write-Host "Using Subscription: $subscriptionId" -ForegroundColor Yellow

# Login to Azure using service principal
Write-Host "Logging in to Azure..." -ForegroundColor Yellow
az login --service-principal --username "6ca1a3a5-0ed9-42e8-820d-4ff3610387af" --password "****************************************" --tenant "b1519f0f-2dbf-4e21-bf34-a686ce97588a"
az account set --subscription $subscriptionId

# Define expected configuration based on environment
if ($Environment -eq "dev") {
    $appGwName = "ar-az-est1-d-cps-agw-001"
    $resourceGroup = "ar-az-est1-d-cps-rg-001"
    $configured = $true
    
    Write-Host "`nChecking Application Gateway in Azure..." -ForegroundColor Yellow
    
    $appGwExists = az network application-gateway show --name $appGwName --resource-group $resourceGroup --query "name" --output tsv 2>$null
    
    if ($appGwExists) {
        Write-Host "✓ Application Gateway '$appGwName' exists in Azure" -ForegroundColor Green
        
        # Get detailed configuration
        $appGwConfig = az network application-gateway show --name $appGwName --resource-group $resourceGroup --output json | ConvertFrom-Json
        
        Write-Host "`n=== Azure Configuration ===" -ForegroundColor Cyan
        Write-Host "Name: $($appGwConfig.name)"
        Write-Host "Location: $($appGwConfig.location)"
        Write-Host "SKU Name: $($appGwConfig.sku.name)"
        Write-Host "SKU Tier: $($appGwConfig.sku.tier)"
        
        if ($appGwConfig.autoscaleConfiguration) {
            Write-Host "Min Capacity: $($appGwConfig.autoscaleConfiguration.minCapacity)"
            Write-Host "Max Capacity: $($appGwConfig.autoscaleConfiguration.maxCapacity)"
        }
        
        Write-Host "`nBackend Pools:"
        foreach ($pool in $appGwConfig.backendAddressPools) {
            $fqdns = $pool.backendAddresses | ForEach-Object { $_.fqdn } | Where-Object { $_ }
            Write-Host "  - $($pool.name): $($fqdns -join ', ')"
        }
        
        Write-Host "`nHTTP Listeners:"
        foreach ($listener in $appGwConfig.httpListeners) {
            Write-Host "  - $($listener.name): $($listener.hostName) (Protocol: $($listener.protocol))"
        }
        
        # Compare with expected configuration
        Write-Host "`n=== Configuration Comparison ===" -ForegroundColor Cyan
        
        $issues = @()
        
        if ($appGwConfig.location -ne "eastus") {
            $issues += "Location mismatch: Azure=$($appGwConfig.location), Expected=eastus"
        }
        
        if ($appGwConfig.sku.name -ne "WAF_v2") {
            $issues += "SKU Name mismatch: Azure=$($appGwConfig.sku.name), Expected=WAF_v2"
        }
        
        if ($appGwConfig.sku.tier -ne "WAF_v2") {
            $issues += "SKU Tier mismatch: Azure=$($appGwConfig.sku.tier), Expected=WAF_v2"
        }
        
        if ($appGwConfig.autoscaleConfiguration) {
            if ($appGwConfig.autoscaleConfiguration.minCapacity -ne 2) {
                $issues += "Min Capacity mismatch: Azure=$($appGwConfig.autoscaleConfiguration.minCapacity), Expected=2"
            }
            if ($appGwConfig.autoscaleConfiguration.maxCapacity -ne 10) {
                $issues += "Max Capacity mismatch: Azure=$($appGwConfig.autoscaleConfiguration.maxCapacity), Expected=10"
            }
        }
        
        if ($issues.Count -eq 0) {
            Write-Host "✓ Configuration is in sync!" -ForegroundColor Green
        } else {
            Write-Host "⚠ Configuration differences found:" -ForegroundColor Yellow
            foreach ($issue in $issues) {
                Write-Host "  - $issue" -ForegroundColor Red
            }
        }
        
    } else {
        Write-Host "✗ Application Gateway '$appGwName' does not exist in Azure" -ForegroundColor Red
        Write-Host "Expected Resource Group: $resourceGroup" -ForegroundColor Yellow
    }
} else {
    Write-Host "`nApplication Gateway is not configured for this environment in the repository" -ForegroundColor Yellow
    
    # Check if any Application Gateway exists in the resource group
    $resourceGroup = switch ($Environment) {
        "qa" { "ar-az-est1-q-cps-rg-001" }
        "sqa" { "ar-az-est1-sqa-cps-rg-001" }
        "prod" { "ar-az-est1-p-cps-rg-001" }
    }
    
    if ($resourceGroup) {
        $existingAppGws = az network application-gateway list --resource-group $resourceGroup --query "[].name" --output tsv 2>$null
        if ($existingAppGws) {
            Write-Host "⚠ Found existing Application Gateways in $resourceGroup :" -ForegroundColor Yellow
            foreach ($appgw in $existingAppGws) {
                Write-Host "  - $appgw" -ForegroundColor Cyan
            }
            Write-Host "These are not managed by Terraform in the current repository configuration" -ForegroundColor Red
        } else {
            Write-Host "✓ No Application Gateways found in $resourceGroup" -ForegroundColor Green
        }
    }
}

Write-Host "`n=== Summary ===" -ForegroundColor Green
Write-Host "Environment: $Environment"
Write-Host "Subscription: $subscriptionId"
if ($Environment -eq "dev") {
    Write-Host "Repository Status: Configured"
} else {
    Write-Host "Repository Status: Not configured"
}

# Logout
az logout
