# Azure Infrastructure Sync Check Scripts

This directory contains scripts to help verify that your Terraform repository configuration is in sync with the actual deployed resources in Azure.

## Application Gateway Sync Check

### Purpose
These scripts compare the Application Gateway configuration defined in your Terraform files with what's actually deployed in Azure, helping you identify any configuration drift.

### Available Scripts

#### PowerShell Script (Windows)
```powershell
.\check-appgw-sync.ps1 -Environment <env>
```

#### Bash Script (Linux/macOS)
```bash
./check-appgw-sync.sh -e <env>
```

### Parameters
- `Environment` / `-e`: The environment to check (dev, qa, sqa, prod)

### Prerequisites
1. Azure CLI installed and configured
2. Service principal credentials (already configured in the scripts)
3. Appropriate permissions to read Azure resources

### Usage Examples

**Check Development Environment:**
```powershell
# PowerShell
.\check-appgw-sync.ps1 -Environment dev

# Bash
./check-appgw-sync.sh -e dev
```

**Check Production Environment:**
```powershell
# PowerShell
.\check-appgw-sync.ps1 -Environment prod

# Bash
./check-appgw-sync.sh -e prod
```

### What the Script Checks

#### For Development Environment:
- Application Gateway: `ar-az-est1-d-cps-agw-001`
- Resource Group: `ar-az-est1-d-cps-rg-001`
- SKU: WAF_v2
- Autoscaling: Min 2, Max 10 instances
- Domains configured:
  - `dev-app.ecps.ca` → Storage + Web App backends
  - `dev-supplier.ecps.ca` → Storage + Web App backends

#### For Other Environments:
- QA: Configuration is commented out in repository
- SQA: No configuration found in repository
- Production: No configuration found in repository

### Output Interpretation

**✓ Green messages**: Configuration matches between repository and Azure
**⚠ Yellow messages**: Warnings or informational messages
**✗ Red messages**: Configuration mismatches or missing resources

### Sample Output
```
=== Application Gateway Sync Check for Environment: dev ===
Using Subscription: 91c43315-5ccf-47d1-85dc-c73e8579b055
Logging in to Azure...
✓ Application Gateway 'ar-az-est1-d-cps-agw-001' exists in Azure

=== Azure Configuration ===
Name: ar-az-est1-d-cps-agw-001
Location: eastus
SKU Name: WAF_v2
SKU Tier: WAF_v2
Min Capacity: 2
Max Capacity: 10

Backend Pools:
  - app-pool-dev-app-ecps-ca: arazest1dadminst001.z13.web.core.windows.net
  - api-pool-dev-app-ecps-ca: ar-az-est1-d-cpsadmin-web-001.azurewebsites.net

=== Configuration Comparison ===
✓ Configuration is in sync!
```

### Troubleshooting

**Authentication Issues:**
- Ensure the service principal has Reader permissions on the target subscription
- Verify the service principal credentials are correct

**Resource Not Found:**
- Check if the resource group exists
- Verify the Application Gateway name matches the expected naming convention
- Ensure you're checking the correct subscription

**Permission Denied:**
- The service principal needs at least Reader role on the subscription or resource group

### Subscription Mapping
- **Non-Production** (dev, qa, sqa): `91c43315-5ccf-47d1-85dc-c73e8579b055`
- **Production** (prod): `16ccf387-4430-45f8-b1b9-561222c09620`

### Security Note
The service principal credentials are embedded in the scripts for convenience. In a production environment, consider:
- Using Azure Key Vault to store credentials
- Using managed identities where possible
- Rotating service principal secrets regularly
