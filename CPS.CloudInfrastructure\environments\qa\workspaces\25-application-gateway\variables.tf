# variable "application_gateway_name" {
#   description = "Name of the Key Vault"
#   type        = string
# }

# variable "location" {
#   description = "The Azure location where the resources will be created."
#   type        = string
#   default     = "East US"
# }

# variable "resource_group_name" {
#   description = "Name of the resource group"
#   type        = string
# }

# variable "public_ip_name" {
#   description = "Name of the public IP address"
#   type        = string
# }

# variable "sku_name" {
#   description = "Name of the SKU"
#   type        = string

# }

# variable "sku_tier" {
#   description = "Tier of the SKU"
#   type        = string

# }



# variable "min_capacity" {
#   description = "Minimum capacity for autoscaling"
#   type        = number
#   default     = 2
# }

# variable "max_capacity" {
#   description = "Maximum capacity for autoscaling"
#   type        = number
#   default     = 10
# }


# variable "appgw_subnet_name" {
#   description = "value of the subnet name"
#   type        = string
# }

# variable "tags" {
#   type        = map(string)
#   description = "Tags to be applied to the resources"
#   default     = {}
# }

# variable "domains" {
#   description = "Map of domain names to their backend configurations"
#   type = map(object({
#     app_backend = object({
#       host_name   = string
#       type        = string                # 'storage' or 'webapp'
#       health_path = optional(string, "/") # Default health check path for SPA
#     })
#     api_backend = object({
#       host_name   = string
#       type        = string                          # 'webapp'
#       health_path = optional(string, "/api/health") # Default health check path for API
#     })
#   }))
#   default = {}
# }
